package org.endipi.enrollment.dto.response;

import lombok.*;

/// Serves as a summary to CourseOffering registration (used in CourseOfferingList.jsx)
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourseRegistrationSummaryResponse {
    private Long courseOfferingId;
    private Long courseId;
    private String courseName;
    private String semesterName;
    private Long teacherId;
    private String teacherName;
    private Long numberOfRegistrations;
}
