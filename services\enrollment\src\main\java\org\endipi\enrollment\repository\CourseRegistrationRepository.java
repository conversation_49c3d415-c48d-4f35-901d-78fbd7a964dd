package org.endipi.enrollment.repository;

import org.endipi.enrollment.dto.response.CourseRegistrationSummaryResponse;
import org.endipi.enrollment.entity.CourseRegistration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CourseRegistrationRepository extends JpaRepository<CourseRegistration, Long> {
    @Query("""
           select new org.endipi.enrollment.dto.response.CourseRegistrationSummaryResponse(
                      cr.courseOffering.id,
                      cr.courseOffering.courseId,
                      null,
                      cr.courseOffering.semester.name,
                      cr.courseOffering.teacherId,
                      null,
                      COALESCE(COUNT(cr.courseOffering.courseId), 0)
                      )
           from CourseRegistration cr
           group by cr.courseOffering.id, cr.courseOffering.courseId
           """)
    Page<CourseRegistrationSummaryResponse> findCourseRegistrationSummariesWithPaging(Pageable pageable);
}
